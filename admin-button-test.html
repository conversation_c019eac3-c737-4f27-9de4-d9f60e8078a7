<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin <PERSON>ton Testing - CSP Compliant</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin: 16px 0;
            padding: 16px;
        }
        .test-passed {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
        .test-failed {
            border-color: #ef4444;
            background-color: #fef2f2;
        }
        .test-pending {
            border-color: #f59e0b;
            background-color: #fffbeb;
        }
        .console-output {
            background-color: #1f2937;
            color: #f9fafb;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 12px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 8px;
        }
        .test-button {
            margin: 4px;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body class="bg-gray-100 p-6">
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-vial text-blue-600 mr-3"></i>
                Admin Button Testing Suite - CSP Compliant
            </h1>
            <p class="text-gray-600 mb-4">
                This test suite verifies that all admin page buttons work correctly with the CSP-compliant event delegation system.
            </p>
            
            <!-- Test Controls -->
            <div class="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-lg">
                <button id="start-all-tests" class="test-button bg-blue-600 text-white">
                    <i class="fas fa-play mr-2"></i>Start All Tests
                </button>
                <button id="clear-results" class="test-button bg-gray-600 text-white">
                    <i class="fas fa-trash mr-2"></i>Clear Results
                </button>
                <button id="open-admin-page" class="test-button bg-green-600 text-white">
                    <i class="fas fa-external-link-alt mr-2"></i>Open Admin Page
                </button>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">Auto-scroll:</span>
                    <input type="checkbox" id="auto-scroll" checked class="rounded">
                </div>
            </div>

            <!-- Test Results Summary -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-blue-600" id="total-tests">0</div>
                    <div class="text-sm text-blue-800">Total Tests</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-green-600" id="passed-tests">0</div>
                    <div class="text-sm text-green-800">Passed</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-red-600" id="failed-tests">0</div>
                    <div class="text-sm text-red-800">Failed</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="pending-tests">0</div>
                    <div class="text-sm text-yellow-800">Pending</div>
                </div>
            </div>
        </div>

        <!-- Test Sections -->
        <div id="test-sections">
            <!-- Logout Button Test -->
            <div class="test-section test-pending" id="logout-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    1. Logout Button Test
                </h3>
                <p class="text-gray-600 mb-3">Tests the logout button functionality with confirmation dialog.</p>
                <button class="test-button bg-blue-600 text-white" data-test="logout">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="logout-console"></div>
            </div>

            <!-- User Management Buttons Test -->
            <div class="test-section test-pending" id="user-management-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-users mr-2"></i>
                    2. User Management Buttons Test
                </h3>
                <p class="text-gray-600 mb-3">Tests Edit and Delete buttons in the Users section.</p>
                <button class="test-button bg-blue-600 text-white" data-test="user-management">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="user-management-console"></div>
            </div>

            <!-- Order Management Buttons Test -->
            <div class="test-section test-pending" id="order-management-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    3. Order Management Buttons Test
                </h3>
                <p class="text-gray-600 mb-3">Tests View and Update buttons in the Orders section.</p>
                <button class="test-button bg-blue-600 text-white" data-test="order-management">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="order-management-console"></div>
            </div>

            <!-- Pagination Buttons Test -->
            <div class="test-section test-pending" id="pagination-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-list mr-2"></i>
                    4. Pagination Buttons Test
                </h3>
                <p class="text-gray-600 mb-3">Tests Previous, Next, and numbered page buttons.</p>
                <button class="test-button bg-blue-600 text-white" data-test="pagination">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="pagination-console"></div>
            </div>

            <!-- Modal Close Buttons Test -->
            <div class="test-section test-pending" id="modal-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-window-close mr-2"></i>
                    5. Modal Close Buttons Test
                </h3>
                <p class="text-gray-600 mb-3">Tests that all modal close/cancel buttons work properly.</p>
                <button class="test-button bg-blue-600 text-white" data-test="modal">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="modal-console"></div>
            </div>

            <!-- Quick Action Buttons Test -->
            <div class="test-section test-pending" id="quick-actions-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-bolt mr-2"></i>
                    6. Quick Action Buttons Test
                </h3>
                <p class="text-gray-600 mb-3">Tests Add Product, View Orders, and Manage Users buttons.</p>
                <button class="test-button bg-blue-600 text-white" data-test="quick-actions">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="quick-actions-console"></div>
            </div>

            <!-- CSP Compliance Test -->
            <div class="test-section test-pending" id="csp-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-shield-alt mr-2"></i>
                    7. CSP Compliance Test
                </h3>
                <p class="text-gray-600 mb-3">Checks for CSP violations and inline scripts/styles.</p>
                <button class="test-button bg-blue-600 text-white" data-test="csp">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="csp-console"></div>
            </div>

            <!-- Server Communication Test -->
            <div class="test-section test-pending" id="server-test">
                <h3 class="text-lg font-semibold mb-3">
                    <i class="fas fa-server mr-2"></i>
                    8. Server Communication Test
                </h3>
                <p class="text-gray-600 mb-3">Verifies API calls and server responses for button actions.</p>
                <button class="test-button bg-blue-600 text-white" data-test="server">
                    <i class="fas fa-play mr-2"></i>Run Test
                </button>
                <div class="console-output hidden" id="server-console"></div>
            </div>
        </div>

        <!-- Test Instructions -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4">
                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                Testing Instructions
            </h2>
            <div class="space-y-3 text-sm text-gray-600">
                <p><strong>1.</strong> Make sure the admin page is open and you are logged in as an admin user.</p>
                <p><strong>2.</strong> Click "Start All Tests" to run all tests automatically, or run individual tests.</p>
                <p><strong>3.</strong> Each test will interact with the admin page and report results here.</p>
                <p><strong>4.</strong> Check the browser console for any JavaScript errors or CSP violations.</p>
                <p><strong>5.</strong> Green sections indicate passed tests, red indicates failures, yellow indicates pending.</p>
                <p><strong>Note:</strong> Some tests require user interaction (like confirming dialogs) - follow the prompts.</p>
            </div>
        </div>
    </div>

    <script src="admin-button-test.js"></script>
</body>
</html>
