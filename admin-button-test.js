class AdminButtonTester {
    constructor() {
        this.adminWindow = null;
        this.testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            pending: 0
        };
        this.tests = [
            'logout',
            'user-management', 
            'order-management',
            'pagination',
            'modal',
            'quick-actions',
            'csp',
            'server'
        ];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTestCounts();
        this.logToConsole('Test suite initialized. Ready to test admin buttons.', 'info');
    }

    setupEventListeners() {
        // Start all tests button
        document.getElementById('start-all-tests').addEventListener('click', () => {
            this.runAllTests();
        });

        // Clear results button
        document.getElementById('clear-results').addEventListener('click', () => {
            this.clearAllResults();
        });

        // Open admin page button
        document.getElementById('open-admin-page').addEventListener('click', () => {
            this.openAdminPage();
        });

        // Individual test buttons
        document.querySelectorAll('[data-test]').forEach(button => {
            button.addEventListener('click', (e) => {
                const testName = e.target.closest('[data-test]').dataset.test;
                this.runTest(testName);
            });
        });
    }

    openAdminPage() {
        this.adminWindow = window.open('/admin.html', 'admin-page', 'width=1200,height=800');
        if (this.adminWindow) {
            this.logToConsole('Admin page opened in new window. Please log in if needed.', 'info');
        } else {
            this.logToConsole('Failed to open admin page. Please check popup blockers.', 'error');
        }
    }

    async runAllTests() {
        this.logToConsole('Starting all tests...', 'info');
        
        if (!this.adminWindow || this.adminWindow.closed) {
            this.logToConsole('Admin page not open. Opening now...', 'warning');
            this.openAdminPage();
            await this.delay(3000); // Wait for page to load
        }

        for (const testName of this.tests) {
            await this.runTest(testName);
            await this.delay(1000); // Delay between tests
        }

        this.logToConsole('All tests completed!', 'success');
    }

    async runTest(testName) {
        this.logToConsole(`Running ${testName} test...`, 'info');
        this.setTestStatus(testName, 'running');

        try {
            switch (testName) {
                case 'logout':
                    await this.testLogoutButton();
                    break;
                case 'user-management':
                    await this.testUserManagementButtons();
                    break;
                case 'order-management':
                    await this.testOrderManagementButtons();
                    break;
                case 'pagination':
                    await this.testPaginationButtons();
                    break;
                case 'modal':
                    await this.testModalCloseButtons();
                    break;
                case 'quick-actions':
                    await this.testQuickActionButtons();
                    break;
                case 'csp':
                    await this.testCSPCompliance();
                    break;
                case 'server':
                    await this.testServerCommunication();
                    break;
                default:
                    throw new Error(`Unknown test: ${testName}`);
            }
            this.setTestStatus(testName, 'passed');
        } catch (error) {
            this.logToConsole(`Test ${testName} failed: ${error.message}`, 'error', testName);
            this.setTestStatus(testName, 'failed');
        }
    }

    async testLogoutButton() {
        if (!this.checkAdminPage()) return;

        const logoutBtn = this.adminWindow.document.getElementById('admin-logout');
        if (!logoutBtn) {
            throw new Error('Logout button not found');
        }

        this.logToConsole('Found logout button, testing click...', 'info', 'logout');
        
        // Override confirm to prevent actual logout during test
        const originalConfirm = this.adminWindow.confirm;
        this.adminWindow.confirm = () => {
            this.logToConsole('Confirmation dialog triggered ✓', 'success', 'logout');
            return false; // Cancel logout for test
        };

        logoutBtn.click();
        
        // Restore original confirm
        this.adminWindow.confirm = originalConfirm;
        
        this.logToConsole('Logout button test completed successfully', 'success', 'logout');
    }

    async testUserManagementButtons() {
        if (!this.checkAdminPage()) return;

        // Navigate to users section
        await this.navigateToSection('users');
        await this.delay(2000);

        const editButtons = this.adminWindow.document.querySelectorAll('[data-action="edit-user"]');
        const deleteButtons = this.adminWindow.document.querySelectorAll('[data-action="delete-user"]');

        this.logToConsole(`Found ${editButtons.length} edit buttons and ${deleteButtons.length} delete buttons`, 'info', 'user-management');

        if (editButtons.length === 0 && deleteButtons.length === 0) {
            throw new Error('No user management buttons found');
        }

        // Test edit button if available
        if (editButtons.length > 0) {
            this.logToConsole('Testing edit user button...', 'info', 'user-management');
            editButtons[0].click();
            await this.delay(1000);
            this.logToConsole('Edit button clicked successfully ✓', 'success', 'user-management');
        }

        // Test delete button if available
        if (deleteButtons.length > 0) {
            const originalConfirm = this.adminWindow.confirm;
            this.adminWindow.confirm = () => {
                this.logToConsole('Delete confirmation dialog triggered ✓', 'success', 'user-management');
                return false; // Cancel delete for test
            };

            this.logToConsole('Testing delete user button...', 'info', 'user-management');
            deleteButtons[0].click();
            
            this.adminWindow.confirm = originalConfirm;
            this.logToConsole('Delete button clicked successfully ✓', 'success', 'user-management');
        }

        this.logToConsole('User management buttons test completed', 'success', 'user-management');
    }

    async testOrderManagementButtons() {
        if (!this.checkAdminPage()) return;

        // Navigate to orders section
        await this.navigateToSection('orders');
        await this.delay(2000);

        const viewButtons = this.adminWindow.document.querySelectorAll('[data-action="view-order"]');
        const updateButtons = this.adminWindow.document.querySelectorAll('[data-action="update-order-status"]');

        this.logToConsole(`Found ${viewButtons.length} view buttons and ${updateButtons.length} update buttons`, 'info', 'order-management');

        if (viewButtons.length === 0 && updateButtons.length === 0) {
            throw new Error('No order management buttons found');
        }

        // Test view button if available
        if (viewButtons.length > 0) {
            this.logToConsole('Testing view order button...', 'info', 'order-management');
            viewButtons[0].click();
            await this.delay(1000);
            
            // Check if modal opened
            const modal = this.adminWindow.document.querySelector('.fixed');
            if (modal) {
                this.logToConsole('View order modal opened successfully ✓', 'success', 'order-management');
                // Close modal
                const closeBtn = modal.querySelector('[data-action="close-modal"]');
                if (closeBtn) closeBtn.click();
            } else {
                this.logToConsole('View order button clicked ✓', 'success', 'order-management');
            }
        }

        // Test update button if available
        if (updateButtons.length > 0) {
            this.logToConsole('Testing update order status button...', 'info', 'order-management');
            updateButtons[0].click();
            await this.delay(1000);
            
            // Check if modal opened
            const modal = this.adminWindow.document.querySelector('.fixed');
            if (modal) {
                this.logToConsole('Update order status modal opened successfully ✓', 'success', 'order-management');
                // Close modal
                const closeBtn = modal.querySelector('[data-action="close-modal"]');
                if (closeBtn) closeBtn.click();
            } else {
                this.logToConsole('Update order status button clicked ✓', 'success', 'order-management');
            }
        }

        this.logToConsole('Order management buttons test completed', 'success', 'order-management');
    }

    async testPaginationButtons() {
        if (!this.checkAdminPage()) return;

        // Navigate to users section (which should have pagination)
        await this.navigateToSection('users');
        await this.delay(2000);

        const paginationButtons = this.adminWindow.document.querySelectorAll('[data-action="change-page"]');
        
        this.logToConsole(`Found ${paginationButtons.length} pagination buttons`, 'info', 'pagination');

        if (paginationButtons.length === 0) {
            this.logToConsole('No pagination buttons found - this may be normal if there is only one page of data', 'warning', 'pagination');
            return;
        }

        // Test first pagination button
        this.logToConsole('Testing pagination button...', 'info', 'pagination');
        paginationButtons[0].click();
        await this.delay(1000);
        
        this.logToConsole('Pagination button clicked successfully ✓', 'success', 'pagination');
        this.logToConsole('Pagination buttons test completed', 'success', 'pagination');
    }

    async testModalCloseButtons() {
        if (!this.checkAdminPage()) return;

        // Try to open a modal first (edit user)
        await this.navigateToSection('users');
        await this.delay(2000);

        const editButtons = this.adminWindow.document.querySelectorAll('[data-action="edit-user"]');
        if (editButtons.length > 0) {
            this.logToConsole('Opening modal to test close button...', 'info', 'modal');
            editButtons[0].click();
            await this.delay(1000);

            const modal = this.adminWindow.document.querySelector('.fixed');
            if (modal) {
                const closeButtons = modal.querySelectorAll('[data-action="close-modal"], [data-action="close-edit-modal"]');
                
                this.logToConsole(`Found ${closeButtons.length} close buttons in modal`, 'info', 'modal');
                
                if (closeButtons.length > 0) {
                    this.logToConsole('Testing modal close button...', 'info', 'modal');
                    closeButtons[0].click();
                    await this.delay(500);
                    
                    // Check if modal was closed
                    const modalAfterClose = this.adminWindow.document.querySelector('.fixed');
                    if (!modalAfterClose) {
                        this.logToConsole('Modal closed successfully ✓', 'success', 'modal');
                    } else {
                        this.logToConsole('Modal close button clicked ✓', 'success', 'modal');
                    }
                } else {
                    throw new Error('No close buttons found in modal');
                }
            } else {
                throw new Error('Modal did not open');
            }
        } else {
            this.logToConsole('No edit buttons available to test modal close', 'warning', 'modal');
        }

        this.logToConsole('Modal close buttons test completed', 'success', 'modal');
    }

    async testQuickActionButtons() {
        if (!this.checkAdminPage()) return;

        // Navigate to dashboard to find quick action buttons
        await this.navigateToSection('dashboard');
        await this.delay(2000);

        const quickActionButtons = this.adminWindow.document.querySelectorAll('.btn-primary, button');
        let addProductBtn = null;
        let viewOrdersBtn = null;
        let manageUsersBtn = null;

        // Find specific buttons by text content
        quickActionButtons.forEach(btn => {
            const text = btn.textContent.trim();
            if (text.includes('Add Product') || text.includes('Add New Product')) {
                addProductBtn = btn;
            } else if (text.includes('View All Orders')) {
                viewOrdersBtn = btn;
            } else if (text.includes('Manage Users')) {
                manageUsersBtn = btn;
            }
        });

        this.logToConsole(`Found quick action buttons - Add Product: ${!!addProductBtn}, View Orders: ${!!viewOrdersBtn}, Manage Users: ${!!manageUsersBtn}`, 'info', 'quick-actions');

        // Test Add Product button
        if (addProductBtn) {
            this.logToConsole('Testing Add Product button...', 'info', 'quick-actions');
            addProductBtn.click();
            await this.delay(1000);
            this.logToConsole('Add Product button clicked successfully ✓', 'success', 'quick-actions');
        }

        // Test View Orders button
        if (viewOrdersBtn) {
            this.logToConsole('Testing View Orders button...', 'info', 'quick-actions');
            viewOrdersBtn.click();
            await this.delay(1000);
            this.logToConsole('View Orders button clicked successfully ✓', 'success', 'quick-actions');
        }

        // Test Manage Users button
        if (manageUsersBtn) {
            this.logToConsole('Testing Manage Users button...', 'info', 'quick-actions');
            manageUsersBtn.click();
            await this.delay(1000);
            this.logToConsole('Manage Users button clicked successfully ✓', 'success', 'quick-actions');
        }

        if (!addProductBtn && !viewOrdersBtn && !manageUsersBtn) {
            throw new Error('No quick action buttons found');
        }

        this.logToConsole('Quick action buttons test completed', 'success', 'quick-actions');
    }

    async testCSPCompliance() {
        if (!this.checkAdminPage()) return;

        this.logToConsole('Checking for CSP compliance...', 'info', 'csp');

        // Check for inline scripts
        const inlineScripts = this.adminWindow.document.querySelectorAll('script:not([src])');
        const inlineEventHandlers = this.adminWindow.document.querySelectorAll('[onclick], [onload], [onchange], [onsubmit]');
        const inlineStyles = this.adminWindow.document.querySelectorAll('[style]');

        this.logToConsole(`Found ${inlineScripts.length} inline scripts`, inlineScripts.length === 0 ? 'success' : 'warning', 'csp');
        this.logToConsole(`Found ${inlineEventHandlers.length} inline event handlers`, inlineEventHandlers.length === 0 ? 'success' : 'warning', 'csp');
        this.logToConsole(`Found ${inlineStyles.length} inline styles`, inlineStyles.length === 0 ? 'success' : 'warning', 'csp');

        // Check console for CSP violations
        const originalConsoleError = this.adminWindow.console.error;
        let cspViolations = 0;
        
        this.adminWindow.console.error = (...args) => {
            const message = args.join(' ');
            if (message.includes('Content Security Policy') || message.includes('CSP')) {
                cspViolations++;
                this.logToConsole(`CSP Violation detected: ${message}`, 'error', 'csp');
            }
            originalConsoleError.apply(this.adminWindow.console, args);
        };

        // Wait a bit to catch any CSP violations
        await this.delay(2000);
        
        this.adminWindow.console.error = originalConsoleError;

        this.logToConsole(`CSP violations detected: ${cspViolations}`, cspViolations === 0 ? 'success' : 'error', 'csp');
        
        if (inlineEventHandlers.length > 0 || cspViolations > 0) {
            throw new Error(`CSP compliance issues found: ${inlineEventHandlers.length} inline handlers, ${cspViolations} violations`);
        }

        this.logToConsole('CSP compliance test completed successfully', 'success', 'csp');
    }

    async testServerCommunication() {
        if (!this.checkAdminPage()) return;

        this.logToConsole('Testing server communication...', 'info', 'server');

        // Check if apiClient is available
        if (!this.adminWindow.apiClient) {
            throw new Error('API client not found in admin window');
        }

        this.logToConsole('API client found ✓', 'success', 'server');

        // Test authentication
        const token = this.adminWindow.apiClient.getToken();
        if (!token) {
            this.logToConsole('No auth token found - user may not be logged in', 'warning', 'server');
        } else {
            this.logToConsole('Auth token found ✓', 'success', 'server');
        }

        // Test API endpoints (if logged in)
        if (token) {
            try {
                this.logToConsole('Testing dashboard API call...', 'info', 'server');
                const dashboardResponse = await this.adminWindow.apiClient.getDashboardAnalytics();
                this.logToConsole(`Dashboard API response: ${dashboardResponse.success ? 'Success' : 'Failed'}`, 
                    dashboardResponse.success ? 'success' : 'warning', 'server');
            } catch (error) {
                this.logToConsole(`Dashboard API error: ${error.message}`, 'warning', 'server');
            }
        }

        this.logToConsole('Server communication test completed', 'success', 'server');
    }

    // Helper methods
    checkAdminPage() {
        if (!this.adminWindow || this.adminWindow.closed) {
            throw new Error('Admin page is not open. Please open the admin page first.');
        }
        return true;
    }

    async navigateToSection(section) {
        if (!this.checkAdminPage()) return;

        const navLink = this.adminWindow.document.querySelector(`[data-section="${section}"]`);
        if (navLink) {
            navLink.click();
            this.logToConsole(`Navigated to ${section} section`, 'info');
        } else {
            this.logToConsole(`Navigation link for ${section} not found`, 'warning');
        }
    }

    setTestStatus(testName, status) {
        const testSection = document.getElementById(`${testName}-test`);
        if (!testSection) return;

        // Remove all status classes
        testSection.classList.remove('test-passed', 'test-failed', 'test-pending');
        
        // Add new status class
        switch (status) {
            case 'passed':
                testSection.classList.add('test-passed');
                this.testResults.passed++;
                if (this.testResults.pending > 0) this.testResults.pending--;
                break;
            case 'failed':
                testSection.classList.add('test-failed');
                this.testResults.failed++;
                if (this.testResults.pending > 0) this.testResults.pending--;
                break;
            case 'running':
                testSection.classList.add('test-pending');
                break;
        }

        this.updateTestCounts();
    }

    updateTestCounts() {
        this.testResults.total = this.tests.length;
        this.testResults.pending = this.testResults.total - this.testResults.passed - this.testResults.failed;

        document.getElementById('total-tests').textContent = this.testResults.total;
        document.getElementById('passed-tests').textContent = this.testResults.passed;
        document.getElementById('failed-tests').textContent = this.testResults.failed;
        document.getElementById('pending-tests').textContent = this.testResults.pending;
    }

    clearAllResults() {
        this.testResults = { total: 0, passed: 0, failed: 0, pending: 0 };
        
        this.tests.forEach(testName => {
            const testSection = document.getElementById(`${testName}-test`);
            if (testSection) {
                testSection.classList.remove('test-passed', 'test-failed');
                testSection.classList.add('test-pending');
            }
            
            const console = document.getElementById(`${testName}-console`);
            if (console) {
                console.innerHTML = '';
                console.classList.add('hidden');
            }
        });

        this.updateTestCounts();
        this.logToConsole('All test results cleared', 'info');
    }

    logToConsole(message, type = 'info', testName = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${message}`;
        
        // Log to browser console
        console.log(logMessage);
        
        // Log to test console if testName provided
        if (testName) {
            const consoleElement = document.getElementById(`${testName}-console`);
            if (consoleElement) {
                consoleElement.classList.remove('hidden');
                
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${type}`;
                
                let icon = '';
                switch (type) {
                    case 'success': icon = '✓'; break;
                    case 'error': icon = '✗'; break;
                    case 'warning': icon = '⚠'; break;
                    case 'info': icon = 'ℹ'; break;
                }
                
                logEntry.innerHTML = `<span style="color: ${this.getLogColor(type)}">${icon} ${logMessage}</span>`;
                consoleElement.appendChild(logEntry);
                
                // Auto-scroll if enabled
                if (document.getElementById('auto-scroll').checked) {
                    consoleElement.scrollTop = consoleElement.scrollHeight;
                }
            }
        }
    }

    getLogColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || '#6b7280';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the test suite when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new AdminButtonTester();
});
