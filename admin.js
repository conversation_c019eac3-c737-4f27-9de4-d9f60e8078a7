// Admin Dashboard JavaScript
class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.init();
    }

    init() {
        console.log('Initializing Admin Dashboard...');
        try {
            this.checkAuthentication();
            this.setupEventListeners();
            console.log('Admin Dashboard initialization completed successfully');
        } catch (error) {
            console.error('Error during Admin Dashboard initialization:', error);
            this.showError('Failed to initialize admin dashboard. Please refresh the page.');
        }
    }

    async checkAuthentication() {
        const token = window.apiClient.getToken();
        const user = window.apiClient.getCurrentUser();

        if (!token || !user) {
            this.showLoginForm();
            return;
        }

        // Verify token is still valid and user is admin
        try {
            const response = await window.apiClient.request('/api/auth/me', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            console.log('Auth check response:', response);

            if (response.success && response.data) {
                const userData = response.data.data || response.data;
                if (userData.role === 'admin' || userData.role === 'super_admin') {
                    this.showDashboard(userData);
                } else {
                    this.showLoginForm();
                }
            } else {
                this.showLoginForm();
            }
        } catch (error) {
            console.error('Authentication check failed:', error);
            this.showLoginForm();
        }
    }

    showLoginForm() {
        document.getElementById('auth-check').classList.remove('hidden');
        document.getElementById('admin-dashboard').classList.add('hidden');
    }

    showDashboard(user) {
        document.getElementById('auth-check').classList.add('hidden');
        document.getElementById('admin-dashboard').classList.remove('hidden');
        
        // Update user info in sidebar
        document.getElementById('admin-name').textContent = `${user.first_name} ${user.last_name}`;
        document.getElementById('admin-role').textContent = user.role === 'super_admin' ? 'Super Administrator' : 'Administrator';

        // Setup logout button event listener now that dashboard is visible
        this.setupLogoutButton();

        // Setup event delegation for all data-action buttons
        this.setupEventDelegation();

        // Load dashboard data
        this.loadDashboardData();
    }

    setupLogoutButton() {
        console.log('Setting up logout button...');
        const logoutBtn = document.getElementById('admin-logout');
        if (logoutBtn) {
            // Remove any existing event listeners
            logoutBtn.replaceWith(logoutBtn.cloneNode(true));
            const newLogoutBtn = document.getElementById('admin-logout');

            newLogoutBtn.addEventListener('click', this.handleLogout.bind(this));
            console.log('Logout button event listener attached');
            console.log('Logout button element:', newLogoutBtn);
        } else {
            console.warn('Logout button not found in setupLogoutButton');
        }
    }

    setupEventDelegation() {
        console.log('Setting up event delegation...');

        // Add event delegation to the document body for all data-action buttons
        document.body.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;

            const action = target.dataset.action;
            console.log('Data-action button clicked:', action, target);

            switch (action) {
                case 'edit-user':
                    e.preventDefault();
                    const userId = target.dataset.userId;
                    if (userId) {
                        this.editUser(parseInt(userId));
                    }
                    break;

                case 'delete-user':
                    e.preventDefault();
                    const deleteUserId = target.dataset.userId;
                    if (deleteUserId) {
                        this.deleteUser(parseInt(deleteUserId));
                    }
                    break;

                case 'close-edit-modal':
                    e.preventDefault();
                    this.closeEditUserModal();
                    break;

                case 'view-order':
                    e.preventDefault();
                    const orderId = target.dataset.orderId;
                    if (orderId) {
                        this.viewOrder(parseInt(orderId));
                    }
                    break;

                case 'update-order-status':
                    e.preventDefault();
                    const updateOrderId = target.dataset.orderId;
                    if (updateOrderId) {
                        this.updateOrderStatus(parseInt(updateOrderId));
                    }
                    break;

                case 'confirm-status-update':
                    e.preventDefault();
                    const confirmOrderId = target.dataset.orderId;
                    if (confirmOrderId) {
                        this.confirmStatusUpdate(parseInt(confirmOrderId));
                    }
                    break;

                case 'change-page':
                    e.preventDefault();
                    const page = target.dataset.page;
                    if (page) {
                        this.changePage(parseInt(page));
                    }
                    break;

                case 'close-modal':
                    e.preventDefault();
                    // Close any modal by removing the closest .fixed element
                    const modal = target.closest('.fixed');
                    if (modal) {
                        modal.remove();
                    }
                    break;

                default:
                    console.warn('Unknown data-action:', action);
            }
        });

        console.log('Event delegation setup complete');
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');

        try {
            // Login form
            const loginForm = document.getElementById('admin-login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', this.handleLogin.bind(this));
                console.log('Login form event listener attached');
            } else {
                console.warn('Login form not found');
            }

            // Note: Logout button is set up in setupLogoutButton() after dashboard is shown

            // Sidebar navigation
            const sidebarLinks = document.querySelectorAll('.sidebar-link');
            console.log(`Found ${sidebarLinks.length} sidebar links`);
            sidebarLinks.forEach((link, index) => {
                link.addEventListener('click', this.handleNavigation.bind(this));
                console.log(`Sidebar link ${index} event listener attached`);
            });

            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const sidebar = document.querySelector('.admin-sidebar');

            if (mobileMenuBtn && sidebar) {
                mobileMenuBtn.addEventListener('click', () => {
                    sidebar.classList.toggle('mobile-open');
                });
                console.log('Mobile menu toggle event listener attached');

                // Close mobile menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                        sidebar.classList.remove('mobile-open');
                    }
                });
                console.log('Mobile menu outside click event listener attached');
            } else {
                console.warn('Mobile menu button or sidebar not found');
            }

            // User menu toggle (if implemented)
            const userMenuBtn = document.getElementById('user-menu-btn');
            if (userMenuBtn) {
                userMenuBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    // Add user menu dropdown functionality here if needed
                });
                console.log('User menu button event listener attached');
            } else {
                console.warn('User menu button not found');
            }

            // Quick action buttons
            this.setupQuickActionButtons();

        } catch (error) {
            console.error('Error setting up event listeners:', error);
            this.showError('Failed to set up event listeners. Some buttons may not work.');
        }

        // Add Product button in header - use more specific approach
        setTimeout(() => {
            const headerButtons = document.querySelectorAll('.btn-primary, a[href="index.html"]');
            headerButtons.forEach(btn => {
                const text = btn.textContent.trim();
                console.log(`Header button found: "${text}"`);

                if (text.includes('Add Product')) {
                    console.log('Setting up header Add Product button');
                    btn.onclick = (e) => {
                        e.preventDefault();
                        console.log('Header Add Product button clicked');
                        this.switchSection('products');
                        this.showAddProductModal();
                    };
                } else if (text.includes('View Site')) {
                    console.log('Setting up View Site button');
                    btn.onclick = (e) => {
                        e.preventDefault();
                        console.log('View Site button clicked');
                        window.open('/', '_blank');
                    };
                }
            });
        }, 200);

        // Also handle the View Site link specifically
        const viewSiteLinks = document.querySelectorAll('a[target="_blank"]');
        viewSiteLinks.forEach(link => {
            if (link.textContent.includes('View Site')) {
                console.log('Setting up View Site link');
                link.onclick = (e) => {
                    e.preventDefault();
                    console.log('View Site link clicked');
                    window.open('/', '_blank');
                };
            }
        });
    }

    setupQuickActionButtons() {
        // Use proper DOM queries for specific buttons only (avoid cloning critical buttons)
        console.log('Setting up quick action buttons...');

        // Get only quick action buttons in admin cards (not ALL buttons)
        const quickActionButtons = document.querySelectorAll('.admin-card button');
        console.log(`Found ${quickActionButtons.length} quick action buttons to check`);

        quickActionButtons.forEach((btn, index) => {
            const text = btn.textContent.trim();
            console.log(`Quick action button ${index}: "${text}"`);

            // Don't clone buttons, just add event listeners
            if (text.includes('Add New Product') || text.includes('Add Product')) {
                console.log('Setting up Add Product button');
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Add Product button clicked');
                    this.switchSection('products');
                    this.showAddProductModal();
                });
            } else if (text.includes('View All Orders')) {
                console.log('Setting up View Orders button');
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('View Orders button clicked');
                    this.switchSection('orders');
                });
            } else if (text.includes('Manage Users')) {
                console.log('Setting up Manage Users button');
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Manage Users button clicked');
                    this.switchSection('users');
                });
            }
        });

        // Setup header buttons separately
        this.setupHeaderButtons();
    }

    setupHeaderButtons() {
        console.log('Setting up header buttons...');

        // Setup header Add Product button
        const headerAddProductBtns = document.querySelectorAll('header .btn-primary');
        headerAddProductBtns.forEach(btn => {
            const text = btn.textContent.trim();
            if (text.includes('Add Product')) {
                console.log('Setting up header Add Product button');
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Header Add Product button clicked');
                    this.switchSection('products');
                    this.showAddProductModal();
                });
            }
        });

        // Setup View Site link
        const viewSiteLinks = document.querySelectorAll('a[target="_blank"]');
        viewSiteLinks.forEach(link => {
            if (link.textContent.includes('View Site')) {
                console.log('Setting up View Site link');
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('View Site link clicked');
                    window.open('/', '_blank');
                });
            }
        });
    }

    showAddProductModal() {
        console.log('showAddProductModal called');
        this.showNotification('Add Product feature coming soon!', 'info');
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('admin-email').value;
        const password = document.getElementById('admin-password').value;
        
        try {
            const response = await window.apiClient.login({ email, password });
            console.log('Admin login response:', response);

            if (response.success && response.data && response.data.data) {
                // Check if user is admin
                if (response.data.data.user.role === 'admin' || response.data.data.user.role === 'super_admin') {
                    window.apiClient.setToken(response.data.data.token);
                    window.apiClient.setCurrentUser(response.data.data.user);
                    this.showDashboard(response.data.data.user);
                } else {
                    this.showError('Access denied. Admin privileges required.');
                }
            } else {
                const errorMessage = response.message || response.data?.message || 'Login failed';
                this.showError(errorMessage);
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('Login failed. Please check your credentials.');
        }
    }

    async handleLogout() {
        // Show confirmation dialog
        const confirmed = confirm('Are you sure you want to logout?');
        if (!confirmed) return;

        const logoutBtn = document.getElementById('admin-logout');
        const originalText = logoutBtn.innerHTML;

        try {
            // Show loading state
            logoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Logging out...';
            logoutBtn.disabled = true;

            // Call server logout endpoint (optional - for session cleanup)
            try {
                const logoutResponse = await window.apiClient.request('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${window.apiClient.getToken()}`
                    }
                });
                if (logoutResponse.success) {
                    console.log('Server logout successful');
                } else {
                    console.warn('Server logout failed:', logoutResponse.message);
                }
            } catch (error) {
                console.warn('Server logout failed, continuing with client logout:', error);
            }

            // Clear client-side data (always do this regardless of server response)
            window.apiClient.removeToken();
            window.apiClient.removeCurrentUser();

            // Clear any other stored data
            localStorage.removeItem('cartItems');
            localStorage.removeItem('wishlistItems');
            sessionStorage.clear();

            // Show success message
            this.showNotification('Logged out successfully', 'success');

            // Redirect to homepage after a short delay
            setTimeout(() => {
                window.location.href = '/';
            }, 1500);

        } catch (error) {
            console.error('Logout error:', error);

            // Reset button state
            logoutBtn.innerHTML = originalText;
            logoutBtn.disabled = false;

            // Even if there's an error, try to clear local data and redirect
            try {
                window.apiClient.removeToken();
                window.apiClient.removeCurrentUser();
                localStorage.removeItem('cartItems');
                localStorage.removeItem('wishlistItems');
                sessionStorage.clear();

                this.showNotification('Logged out (with errors)', 'warning');
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } catch (clearError) {
                console.error('Failed to clear local data:', clearError);
                this.showError('Logout failed. Please clear your browser data manually.');
            }
        }
    }

    handleNavigation(e) {
        e.preventDefault();
        
        const section = e.currentTarget.dataset.section;
        if (section) {
            this.switchSection(section);
        }
    }

    switchSection(section) {
        console.log(`Switching to section: ${section}`);

        // Update active sidebar link
        const sidebarLinks = document.querySelectorAll('.sidebar-link');
        console.log(`Found ${sidebarLinks.length} sidebar links`);
        sidebarLinks.forEach(link => {
            link.classList.remove('active');
        });

        const targetLink = document.querySelector(`[data-section="${section}"]`);
        if (targetLink) {
            targetLink.classList.add('active');
            console.log(`Activated sidebar link for section: ${section}`);
        } else {
            console.warn(`No sidebar link found for section: ${section}`);
        }
        
        // Hide all sections
        document.querySelectorAll('.section').forEach(sec => {
            sec.classList.add('hidden');
        });
        
        // Show selected section
        document.getElementById(`${section}-section`).classList.remove('hidden');
        
        // Update page title
        const titles = {
            dashboard: { title: 'Dashboard', subtitle: 'Welcome to your admin dashboard' },
            products: { title: 'Products', subtitle: 'Manage your product catalog' },
            orders: { title: 'Orders', subtitle: 'View and manage customer orders' },
            users: { title: 'Users', subtitle: 'Manage user accounts and permissions' },
            analytics: { title: 'Analytics', subtitle: 'View detailed analytics and reports' },
            settings: { title: 'Settings', subtitle: 'Configure your store settings' }
        };
        
        const pageInfo = titles[section] || { title: 'Dashboard', subtitle: 'Welcome to your admin dashboard' };
        document.getElementById('page-title').textContent = pageInfo.title;
        document.getElementById('page-subtitle').textContent = pageInfo.subtitle;
        
        this.currentSection = section;
        
        // Load section-specific data
        this.loadSectionData(section);
    }

    async loadSectionData(section) {
        switch (section) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'products':
                await this.loadProductsData();
                break;
            case 'orders':
                await this.loadOrdersData();
                break;
            case 'users':
                await this.loadUsersData();
                break;
            case 'analytics':
                await this.loadAnalyticsData();
                break;
            case 'settings':
                await this.loadSettingsData();
                break;
        }
    }

    async loadDashboardData() {
        try {
            const response = await window.apiClient.getDashboardAnalytics();
            
            if (response.success && response.data) {
                this.updateDashboardStats(response.data.totals);
                this.updateRecentOrders(response.data.recentOrders);
                this.updatePopularProducts(response.data.popularProducts);
            }
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('Failed to load dashboard data');
        }
    }

    updateDashboardStats(totals) {
        document.getElementById('total-users').textContent = totals.users.toLocaleString();
        document.getElementById('total-products').textContent = totals.products.toLocaleString();
        document.getElementById('total-orders').textContent = totals.orders.toLocaleString();
        document.getElementById('total-revenue').textContent = `$${totals.revenue.toLocaleString()}`;
    }

    updateRecentOrders(orders) {
        const container = document.getElementById('recent-orders');
        
        if (!orders || orders.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">No recent orders</p>';
            return;
        }

        container.innerHTML = orders.map(order => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-800">#${order.order_number}</p>
                    <p class="text-sm text-gray-600">${order.customer_name}</p>
                </div>
                <div class="text-right">
                    <p class="font-medium text-gray-800">$${order.total_amount}</p>
                    <span class="text-xs px-2 py-1 rounded-full ${this.getStatusColor(order.status)}">${order.status}</span>
                </div>
            </div>
        `).join('');
    }

    updatePopularProducts(products) {
        const container = document.getElementById('popular-products');
        
        if (!products || products.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">No product data available</p>';
            return;
        }

        container.innerHTML = products.map(product => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-800">${product.name}</p>
                    <p class="text-sm text-gray-600">Stock: ${product.stock_quantity}</p>
                </div>
                <div class="text-right">
                    <p class="font-medium text-gray-800">${product.total_sold || 0} sold</p>
                    <p class="text-sm text-gray-600">$${product.price}</p>
                </div>
            </div>
        `).join('');
    }



    getStatusColor(status) {
        const colors = {
            pending: 'bg-yellow-100 text-yellow-800',
            processing: 'bg-blue-100 text-blue-800',
            shipped: 'bg-purple-100 text-purple-800',
            delivered: 'bg-green-100 text-green-800',
            cancelled: 'bg-red-100 text-red-800',
            refunded: 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    }

    async loadProductsData() {
        // Placeholder for products management
        console.log('Loading products data...');
    }

    async loadOrdersData(page = 1) {
        try {
            const response = await window.apiClient.getAdminOrders({ page, limit: 20 });

            if (response.success && response.data) {
                this.renderOrdersSection(response.data);
            }
        } catch (error) {
            console.error('Failed to load orders data:', error);
            this.showError('Failed to load orders data');
        }
    }

    renderOrdersSection(data) {
        const ordersSection = document.getElementById('orders-section');
        if (!ordersSection) return;

        ordersSection.innerHTML = `
            <div class="admin-card rounded-xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800">Order Management</h3>
                    <div class="flex items-center space-x-3">
                        <input type="text" placeholder="Search orders..." class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="order-search">
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="status-filter">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="shipped">Shipped</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="orders-table-body">
                            ${data.orders.map(order => this.renderOrderRow(order)).join('')}
                        </tbody>
                    </table>
                </div>

                ${data.pagination ? this.renderPagination(data.pagination) : ''}
            </div>
        `;

        // Add event listeners for search and filter
        this.setupOrdersEventListeners();
    }

    renderOrderRow(order) {
        const statusBadge = `<span class="px-2 py-1 text-xs font-medium ${this.getStatusColor(order.status)} rounded-full">${order.status}</span>`;

        return `
            <tr>
                <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">#${order.order_number}</div>
                    <div class="text-sm text-gray-500">ID: ${order.id}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${order.customer_name || 'Guest'}</div>
                    <div class="text-sm text-gray-500">${order.customer_email || order.guest_email}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">${statusBadge}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$${order.total_amount}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(order.created_at).toLocaleDateString()}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2">
                        <button data-action="view-order" data-order-id="${order.id}" class="text-blue-600 hover:text-blue-900 px-3 py-1 rounded hover:bg-blue-50 transition-colors">View</button>
                        <button data-action="update-order-status" data-order-id="${order.id}" class="text-green-600 hover:text-green-900 px-3 py-1 rounded hover:bg-green-50 transition-colors">Update</button>
                    </div>
                </td>
            </tr>
        `;
    }

    setupOrdersEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('order-search');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.filterOrders();
            }, 300));
        }

        // Status filter
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filterOrders();
            });
        }
    }

    async filterOrders() {
        const search = document.getElementById('order-search')?.value || '';
        const status = document.getElementById('status-filter')?.value || '';

        try {
            const response = await window.apiClient.getAdminOrders({
                page: 1,
                limit: 20,
                search,
                status
            });

            if (response.success && response.data) {
                const tbody = document.getElementById('orders-table-body');
                if (tbody) {
                    tbody.innerHTML = response.data.orders.map(order => this.renderOrderRow(order)).join('');
                }
            }
        } catch (error) {
            console.error('Failed to filter orders:', error);
        }
    }

    async loadUsersData(page = 1) {
        try {
            const response = await window.apiClient.getAdminUsers({ page, limit: 20 });

            if (response.success && response.data) {
                this.renderUsersSection(response.data);
            }
        } catch (error) {
            console.error('Failed to load users data:', error);
            this.showError('Failed to load users data');
        }
    }

    renderUsersSection(data) {
        const usersSection = document.getElementById('users-section');
        if (!usersSection) return;

        usersSection.innerHTML = `
            <div class="admin-card rounded-xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800">User Management</h3>
                    <div class="flex items-center space-x-3">
                        <input type="text" placeholder="Search users..." class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="user-search">
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="role-filter">
                            <option value="">All Roles</option>
                            <option value="user">Users</option>
                            <option value="admin">Admins</option>
                            <option value="super_admin">Super Admins</option>
                        </select>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="users-table-body">
                            ${data.users.map(user => this.renderUserRow(user)).join('')}
                        </tbody>
                    </table>
                </div>

                ${data.pagination ? this.renderPagination(data.pagination) : ''}
            </div>
        `;

        // Add event listeners for search and filter
        this.setupUsersEventListeners();
    }

    renderUserRow(user) {
        const statusBadge = user.is_verified ?
            '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Verified</span>' :
            '<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Unverified</span>';

        const roleBadge = this.getRoleBadge(user.role);

        return `
            <tr>
                <td class="px-4 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                            ${user.first_name.charAt(0).toUpperCase()}
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${user.first_name} ${user.last_name}</div>
                            <div class="text-sm text-gray-500">ID: ${user.id}</div>
                        </div>
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${user.email}</td>
                <td class="px-4 py-4 whitespace-nowrap">${roleBadge}</td>
                <td class="px-4 py-4 whitespace-nowrap">${statusBadge}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(user.created_at).toLocaleDateString()}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2">
                        <button data-action="edit-user" data-user-id="${user.id}" class="text-blue-600 hover:text-blue-900 px-3 py-1 rounded hover:bg-blue-50 transition-colors">Edit</button>
                        ${user.role !== 'super_admin' ? `<button data-action="delete-user" data-user-id="${user.id}" class="text-red-600 hover:text-red-900 px-3 py-1 rounded hover:bg-red-50 transition-colors">Delete</button>` : ''}
                    </div>
                </td>
            </tr>
        `;
    }

    getRoleBadge(role) {
        const badges = {
            'user': '<span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">User</span>',
            'admin': '<span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">Admin</span>',
            'super_admin': '<span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Super Admin</span>'
        };
        return badges[role] || badges['user'];
    }

    setupUsersEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('user-search');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.filterUsers();
            }, 300));
        }

        // Role filter
        const roleFilter = document.getElementById('role-filter');
        if (roleFilter) {
            roleFilter.addEventListener('change', () => {
                this.filterUsers();
            });
        }
    }

    async filterUsers() {
        const search = document.getElementById('user-search')?.value || '';
        const role = document.getElementById('role-filter')?.value || '';

        try {
            const response = await window.apiClient.getAdminUsers({
                page: 1,
                limit: 20,
                search,
                role
            });

            if (response.success && response.data) {
                const tbody = document.getElementById('users-table-body');
                if (tbody) {
                    tbody.innerHTML = response.data.users.map(user => this.renderUserRow(user)).join('');
                }
            }
        } catch (error) {
            console.error('Failed to filter users:', error);
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // User management methods
    async editUser(userId) {
        try {
            // Get user data first
            const response = await window.apiClient.getAdminUsers();
            if (!response.success) {
                this.showError('Failed to load user data');
                return;
            }

            const user = response.data.users.find(u => u.id === userId);
            if (!user) {
                this.showError('User not found');
                return;
            }

            // Show edit user modal
            this.showEditUserModal(user);
        } catch (error) {
            console.error('Failed to load user for editing:', error);
            this.showError('Failed to load user data');
        }
    }

    showEditUserModal(user) {
        // Create modal HTML
        const modalHtml = `
            <div id="edit-user-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">Edit User</h3>
                        <button data-action="close-edit-modal" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="edit-user-form" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                            <input type="text" id="edit-first-name" value="${user.first_name || ''}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                            <input type="text" id="edit-last-name" value="${user.last_name || ''}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" id="edit-email" value="${user.email || ''}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                            <select id="edit-role" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="user" ${user.role === 'user' ? 'selected' : ''}>User</option>
                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
                                <option value="super_admin" ${user.role === 'super_admin' ? 'selected' : ''}>Super Admin</option>
                            </select>
                        </div>

                        <div class="flex space-x-3 pt-4">
                            <button type="submit" class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                                Update User
                            </button>
                            <button type="button" data-action="close-edit-modal"
                                    class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add form submit handler
        document.getElementById('edit-user-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleEditUserSubmit(user.id);
        });
    }

    closeEditUserModal() {
        const modal = document.getElementById('edit-user-modal');
        if (modal) {
            modal.remove();
        }
    }

    async handleEditUserSubmit(userId) {
        try {
            const firstName = document.getElementById('edit-first-name').value.trim();
            const lastName = document.getElementById('edit-last-name').value.trim();
            const email = document.getElementById('edit-email').value.trim();
            const role = document.getElementById('edit-role').value;

            if (!firstName || !lastName || !email) {
                this.showError('Please fill in all required fields');
                return;
            }

            // Update user role (this is the main functionality we have API for)
            const response = await window.apiClient.updateUserRole(userId, role);

            if (response.success) {
                this.showNotification('User updated successfully', 'success');
                this.closeEditUserModal();
                this.loadUsersData(); // Reload users list
            } else {
                this.showError(response.message || 'Failed to update user');
            }
        } catch (error) {
            console.error('Failed to update user:', error);
            this.showError('Failed to update user');
        }
    }

    async deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) return;

        try {
            const response = await window.apiClient.deleteUser(userId);
            if (response.success) {
                this.showNotification('User deleted successfully', 'success');
                this.loadUsersData(); // Reload users list
            } else {
                this.showError(response.message || 'Failed to delete user');
            }
        } catch (error) {
            console.error('Failed to delete user:', error);
            this.showError('Failed to delete user');
        }
    }

    // Order management methods
    async viewOrder(orderId) {
        try {
            // Get order data first
            const response = await window.apiClient.getAdminOrders();
            if (!response.success) {
                this.showError('Failed to load order data');
                return;
            }

            const order = response.data.orders.find(o => o.id === orderId);
            if (!order) {
                this.showError('Order not found');
                return;
            }

            // Show view order modal
            this.showViewOrderModal(order);
        } catch (error) {
            console.error('Failed to load order for viewing:', error);
            this.showError('Failed to load order data');
        }
    }

    showViewOrderModal(order) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Order Details - #${order.order_number}</h3>
                    <button data-action="close-modal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Customer</label>
                            <p class="text-sm text-gray-900">${order.customer_name || 'Guest'}</p>
                            <p class="text-sm text-gray-500">${order.customer_email || order.guest_email}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <span class="px-2 py-1 text-xs font-medium ${this.getStatusColor(order.status)} rounded-full">${order.status}</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                            <p class="text-sm text-gray-900">$${order.total_amount}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Order Date</label>
                            <p class="text-sm text-gray-900">${new Date(order.created_at).toLocaleDateString()}</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button data-action="close-modal" class="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">Close</button>
                        <button data-action="update-order-status" data-order-id="${order.id}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">Update Status</button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    async updateOrderStatus(orderId) {
        // Show status update modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Update Order Status</h3>
                    <button data-action="close-modal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                        <select id="new-status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="shipped">Shipped</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    <div class="flex space-x-3 pt-4">
                        <button data-action="close-modal" class="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors duration-300">
                            Cancel
                        </button>
                        <button data-action="confirm-status-update" data-order-id="${orderId}" class="flex-1 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors duration-300">
                            Update
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    async confirmStatusUpdate(orderId) {
        const newStatus = document.getElementById('new-status')?.value;
        if (!newStatus) return;

        try {
            const response = await window.apiClient.updateOrderStatus(orderId, newStatus);
            if (response.success) {
                this.showNotification('Order status updated successfully', 'success');
                this.loadOrdersData(); // Reload orders list

                // Close modal
                document.querySelector('.fixed')?.remove();
            }
        } catch (error) {
            console.error('Failed to update order status:', error);
            this.showError('Failed to update order status');
        }
    }

    // Pagination helper
    renderPagination(pagination) {
        if (!pagination || pagination.totalPages <= 1) return '';

        const { currentPage, totalPages } = pagination;
        let paginationHtml = '<div class="flex items-center justify-between mt-6">';

        paginationHtml += `<div class="text-sm text-gray-700">
            Showing ${((currentPage - 1) * pagination.itemsPerPage) + 1} to ${Math.min(currentPage * pagination.itemsPerPage, pagination.total)} of ${pagination.total} results
        </div>`;

        paginationHtml += '<div class="flex items-center space-x-2">';

        // Previous button
        if (currentPage > 1) {
            paginationHtml += `<button data-action="change-page" data-page="${currentPage - 1}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
        }

        // Page numbers
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            const isActive = i === currentPage;
            paginationHtml += `<button data-action="change-page" data-page="${i}" class="px-3 py-2 text-sm font-medium ${isActive ? 'text-blue-600 bg-blue-50 border-blue-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'} border rounded-md">${i}</button>`;
        }

        // Next button
        if (currentPage < totalPages) {
            paginationHtml += `<button data-action="change-page" data-page="${currentPage + 1}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
        }

        paginationHtml += '</div></div>';
        return paginationHtml;
    }

    async changePage(page) {
        console.log('Changing to page:', page, 'Current section:', this.currentSection);

        try {
            switch (this.currentSection) {
                case 'users':
                    await this.loadUsersData(page);
                    break;
                case 'orders':
                    await this.loadOrdersData(page);
                    break;
                default:
                    this.showNotification('Pagination not available for this section', 'info');
            }
        } catch (error) {
            console.error('Failed to change page:', error);
            this.showError('Failed to load page data');
        }
    }

    async loadAnalyticsData() {
        // Placeholder for analytics
        console.log('Loading analytics data...');
    }

    async loadSettingsData() {
        // Placeholder for settings
        console.log('Loading settings data...');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

        // Set notification style based on type
        switch (type) {
            case 'success':
                notification.className += ' bg-green-500 text-white';
                break;
            case 'error':
                notification.className += ' bg-red-500 text-white';
                break;
            case 'warning':
                notification.className += ' bg-yellow-500 text-white';
                break;
            default:
                notification.className += ' bg-blue-500 text-white';
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize admin dashboard when DOM is loaded
function initializeAdminDashboard() {
    console.log('DOM ready, initializing Admin Dashboard...');
    try {
        // Check if apiClient is available
        if (typeof window.apiClient === 'undefined') {
            console.error('API Client not found. Make sure api-config.js is loaded before admin.js');
            setTimeout(initializeAdminDashboard, 100); // Retry after 100ms
            return;
        }

        window.adminDashboard = new AdminDashboard();
        console.log('Admin Dashboard instance created successfully');
    } catch (error) {
        console.error('Failed to initialize Admin Dashboard:', error);
        // Show error message to user
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg z-50';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span>Failed to initialize admin dashboard. Please refresh the page.</span>
            </div>
        `;
        document.body.appendChild(errorDiv);
    }
}

// Multiple ways to ensure initialization happens
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAdminDashboard);
} else {
    // DOM is already ready
    initializeAdminDashboard();
}

// Fallback initialization
window.addEventListener('load', () => {
    if (!window.adminDashboard) {
        console.log('Fallback initialization triggered');
        initializeAdminDashboard();
    }
});
